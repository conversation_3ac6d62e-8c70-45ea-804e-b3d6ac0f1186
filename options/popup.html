<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="options_all.css"/>
  <link rel="stylesheet" href="popup_switch.css"/>
  <style>
    body {
      text-align: center;
    }
    div {
      margin: 10px;
    }
  </style>
</head>
<body style="width:290px">
<div><strong>Bypass Paywalls Clean <span id="version"></span></strong><span id="site_switch_span">&nbsp;&nbsp;</span></div>
<div><a href="options.html" target="_blank">Options</a> |
<a href="options_custom.html" target="_blank">Custom</a> |
<a href="/README.html" target="_blank">Help</a> |
<a href="https://x.com/Magnolia1234B" target="_blank">X</a> |
<a href="https://xcancel.com/Magnolia1234B" target="_blank">Nitter</a></div>
<div><a href="https://gitflic.ru/project/magnolia1234/bypass-paywalls-chrome-clean/blob/raw?file=changelog.txt" target="_blank">Changelog</a> |
<button id="clear_cookies" title="clear cookies (and local storage) for current site">clear cookies<br>(& permission)</button> |
<button id="button-close" title="close popup">close</button></div>
<div><span id="version_new"></span></div>
<div><span>* for unlisted sites: first clear cookies (X = no fix) & block general paywall-scripts (in options) or use custom sites/reader view</span></div>
<div><span id="archive"></span></div>
<script id="popup" src="version.js"></script>
<script src="popup.js"></script>
</body>
</html>
