<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Bypass Paywalls Clean Options Excluded</title>
  <link rel="stylesheet" href="options_all.css"/>
</head>
<body>
  <h2>Excluded Sites</h2>
  <div style="width:90%;">
    Add excluded sites/domains (for your subscriptions) as a comma-separated list (www.-prefix and spaces are removed).<br>
    You can also exclude a specific domain which is grouped in options.<br>
    Checked sites in options are ignored (to still enable select all).<br>
  </div>
  <div style="clear:both;"></div>
  <div style="width:90%;">
    <h3>Sites</h3>
  </div>
  <br>
  <div id='excluded_sites'></div>
    <br>
  <div id="status"></div>
  <div id="error"></div>
  <span style='float:left;padding-bottom:50px'>
    <button id="save">Save</button>
    <button id="sort">Sort</button>
    <button><a href="options.html" style="text-decoration:none;color:inherit">Options</a></button>
  </span>

 <script src="options_excluded.js"></script>
</body>
</html>
