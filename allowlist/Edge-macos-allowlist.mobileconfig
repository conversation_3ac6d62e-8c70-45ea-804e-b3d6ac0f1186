<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>PayloadContent</key>
    <array>
      <dict>
        <key>PayloadContent</key>
          <dict>
            <key>com.microsoft.Edge</key>
              <dict>
                <key>Forced</key>
                <array>
                  <dict>
                    <key>mcx_preference_settings</key>
                    <dict>


<key>ExtensionInstallAllowlist</key>
<array>
<string>lkbebcjgcmobigpeffafkodonchffocl</string>
</array>


              </dict>
            </dict>
          </array>
        </dict>
      </dict>
      <key>PayloadDisplayName</key>
      <string>MS Edge ExtensionInstallAllowlist - add BPC ID to override browser's restriction</string>
      <key>PayloadEnabled</key>
      <true/>
      <key>PayloadIdentifier</key>
      <string>com.example.Edge.example.bpc</string>
      <key>PayloadType</key>
      <string>com.apple.ManagedClient.preferences</string>
      <key>PayloadUUID</key>
      <string>a5c53548-65aa-11e7-907b-a6006ad3dba0</string>
      <key>PayloadVersion</key>
      <integer>1</integer>
    </dict>
  </array>
  <key>PayloadDescription</key>
  <string>Bypass Paywalls Chrome Clean - MS Edge</string>
  <key>PayloadDisplayName</key>
  <string>MS Edge ExtensionInstallAllowlist for Bypass Paywalls Clean</string>
  <key>PayloadIdentifier</key>
  <string>com.example.Edge.example.bpc</string>
  <key>PayloadOrganization</key>
  <string>Bypass Paywalls Chrome Clean</string>
  <key>PayloadRemovalDisallowed</key>
  <false/>
  <key>PayloadScope</key>
  <string>User</string>
  <key>PayloadType</key>
  <string>Configuration</string>
  <key>PayloadUUID</key>
  <string>33e27889678746574b5f4882e1d3985764907861895746574f95fa247c6003d0cd</string>
  <key>PayloadVersion</key>
  <integer>1</integer>
</dict>
</plist>
